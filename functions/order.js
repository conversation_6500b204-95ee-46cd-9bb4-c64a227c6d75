const { Timestamp } = require('firebase-admin/firestore');
const fetch = require('node-fetch');

const destinationOriginMap = {
  '霍尔果斯': 'Horgos Kazakhstan'
}

async function routeDistance(origin, destination) {
  try {
    const response = await fetch('https://routes.googleapis.com/directions/v2:computeRoutes', {
      method: 'post',
      body: JSON.stringify({
        'origin': {
          'address': origin
        },
        'destination': {
          'address': destination
        }
      }),
      headers: {
        'X-Goog-Api-Key': process.env.GOOGLE_MAPS_API_KEY,
        'X-Goog-FieldMask': 'routes.distanceMeters',
        'Content-Type': 'application/json'
      }
    });
    const data = await response.json();
    return data.routes[0].distanceMeters;
  } catch (e) {
    console.log(e);
    return null;
  }
}

async function updateStatus(firestore, id) {
  const larkClient = require('./lark-client.js');
  const fields = await larkClient.searchRecord(larkClient.orderTableConfig, id)
  const orderId = fields['微信小程序订单ID'][0].text
  const account = fields['下单账户'][0].text
  const status = fields['当前状态']['value'][0].text
  const origin = fields['起始地']
  const pickupAddress = fields['取货详细地址'] && fields['取货详细地址'][0].text
  const expectedLoadingDate = fields['期望装货时间']
  const destinationCountry = fields['目的国家']
  const destinationCity = fields['目的城市(wx)'] && fields['目的城市(wx)'][0].text
  const destinationAddress = fields['卸货详细地址'] && fields['卸货详细地址'][0].text
  const type = fields['车辆类型']
  const volumes = fields['车载体积']
  const tonnage = fields['车载吨位']
  const length = fields['车辆长度']
  const position = fields['装载位数']
  const goods = fields['货物种类'] && fields['货物种类'][0].text
  const targetPrice = fields['目标价格']

  const orderRef = firestore.collection('orders').doc(orderId);
  const exists = (await orderRef.get()).exists;
  if (!exists) {
    await orderRef.set({
      createdAt: Timestamp.now()
    });
  }
  await orderRef.set({
    orderId,
    recordId: id,
    account,
    status,
    origin,
    pickupAddress,
    expectedLoadingDate: Timestamp.fromMillis(expectedLoadingDate),
    destinationCountry,
    destinationCity,
    destinationAddress,
    type,
    volumes,
    tonnage,
    length,
    position,
    goods,
    targetPrice,
    updatedAt: Timestamp.now()
  }, { merge: true });

  const doc = (await orderRef.get()).data();
  if (doc.distance == null) {
    const distance = await routeDistance(destinationOriginMap[origin], `${destinationCountry}${destinationCity}`);
    if (distance != null) {
      await orderRef.set({
        distance
      }, { merge: true });
    }
  }

  await firestore.collection('orderRecords').add({
    orderId,
    status,
    account,
    createdAt: Timestamp.now()
  });

  return { code: 0, message: 'OK' };
}

module.exports = {
  updateStatus
}