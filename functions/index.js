const { onSchedule } = require("firebase-functions/v2/scheduler");
const { onRequest, onCall, HttpsError } = require("firebase-functions/v2/https");
require("firebase-functions/logger/compat");

const admin = require("firebase-admin");
const { syncDrivers, syncTrucks, syncQuotations } = require("./scheduler.js");
const genQuotations = require("./quotation-gen.js");
const { onDocumentCreated } = require("firebase-functions/firestore");
const { createUser, findDrivers, findTruck, downloadAttachment, deleteDocAndSyncLark, createBankInfo } = require("./user.js");
const { bodyFromBasicAuthRequest } = require("./utils.js");
const { updateStatus } = require("./order.js");
const { getFirestore } = require("firebase-admin/firestore");
const { createDriverVerification, updateDriverVerification, updateTruckVerification, createTruckVerification } = require("./verification.js");
admin.initializeApp();
const firestore = getFirestore();
firestore.settings({ ignoreUndefinedProperties: true });

exports.syncInfoFromLark = onSchedule("every day 00:00", async (event) => {
  const larkClient = require('./lark-client.js');
  await syncDrivers(firestore, larkClient);
  await syncTrucks(firestore, larkClient);
  await syncQuotations(firestore, larkClient);
});

exports.larkOrderUpdate = onRequest(
  async (req, res) => {
    const body = bodyFromBasicAuthRequest(req, res, process.env.LARK_ORDER_UPDATE_AUTH, 'POST', ['id', 'type'])
    if (body != null) {
      const { id, type } = body;
      try {
        switch (type) {
          case 1: //update status
            res.status(200).send(await updateStatus(firestore, id));
            break;
          default:
            res.status(500).send({
              code: -1,
              error: 'Invalid action'
            });
            break;
        }
      } catch (error) {
        console.error(error);
        res.status(500).send(error);
      }
    }
  }
);

exports.larkGenerateQuotations = onRequest(
  async (req, res) => {
    const body = bodyFromBasicAuthRequest(req, res, process.env.LARK_QUOTATION_GEN_AUTH, 'POST', ['id'])
    if (body != null) {
      const { id } = body;
      try {
        const message = await genQuotations(firestore, id);
        res.status(200).send(message);
      } catch (error) {
        console.error(error);
        res.status(500).send(error);
      }
    }
  }
);

exports.findDriversWithPhoneNumber = onCall({
  enforceAppCheck: true,
}, async (request) => {
  const { data: { phone } } = request;
  if (!phone) {
    return new HttpsError('invalid-argument', 'Bad Request')
  }

  return await findDrivers(firestore, phone)
});

exports.findTruckWithLicense = onCall({
  enforceAppCheck: true,
}, async (request) => {
  const { data: { license } } = request;
  if (!license) {
    return new HttpsError('invalid-argument', 'Bad Request')
  }

  return await findTruck(firestore, license)
});

exports.getPassportFromLark = onCall({
  enforceAppCheck: true,
}, async (request) => {
  return await downloadAttachment(firestore,
    request,
    'driverTableConfig',
    'passport',
    'drivers'
  );
});

exports.getLicenseFromLark = onCall({
  enforceAppCheck: true,
}, async (request) => {
  return await downloadAttachment(firestore,
    request,
    'truckTableConfig',
    'license1',
    'trucks'
  );
});

exports.larkVerificationUpdate = onRequest(
  async (req, res) => {
    const body = bodyFromBasicAuthRequest(req, res, process.env.LARK_VERIFICATION_UPDATE_AUTH, 'POST', ['id', 'type'])
    if (body != null) {
      const { id, type } = body;
      try {
        switch (type) {
          case 1: // driver verification
            res.status(200).send(await updateDriverVerification(firestore, id));
            break;
          case 2: // truck verification
            res.status(200).send(await updateTruckVerification(firestore, id));
            break;
          default:
            res.status(500).send({
              code: -1,
              error: 'Invalid action'
            });
            break;
        }
      } catch (error) {
        console.error(error);
        res.status(500).send(error);
      }
    }
  }
);

exports.onUserCreated = onDocumentCreated("users/{userId}", async (event) => {
  await createUser(firestore, event);
});

exports.onDriverVerificationCreated = onDocumentCreated("driverVerification/{userId}", async (event) => {
  await createDriverVerification(firestore, event);
});

exports.onTruckVerificationCreated = onDocumentCreated("truckVerification/{docId}", async (event) => {
  await createTruckVerification(firestore, event);
});

exports.deleteFirestoreDocAndSyncLark = onCall({
  enforceAppCheck: true,
}, async (request) => {
  return await deleteDocAndSyncLark(firestore, request);
});

exports.onBankInfoCreated = onDocumentCreated("bankInfo/{docId}", async (event) => {
  await createBankInfo(firestore, event);
});