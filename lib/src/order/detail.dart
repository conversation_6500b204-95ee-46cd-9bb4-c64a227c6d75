import 'package:driver/src/mixins/page.dart';
import 'package:driver/src/model/firestore.dart';
import 'package:driver/src/utils/const.dart';
import 'package:driver/src/utils/convenient.dart';
import 'package:flutter/material.dart';
import 'package:lsapp/lsapp.dart';

class OrderDetailView extends StatefulWidget {
  const OrderDetailView({super.key});

  static const String routeName = 'order_detail';

  @override
  State<StatefulWidget> createState() => _OrderDetailViewState();
}

class _OrderDetailViewState extends State<OrderDetailView> with PageMixin {
  Order? _order;
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      setState(() {
        _order = ModalRoute.of(context)!.settings.arguments as Order?;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return pageBase(appLoc(context).orderDetail, orderInfo(),
        useTransparentAppBar: true,
        actions: [
          IconButton(onPressed: () {}, icon: Icon(Icons.library_books_outlined))
        ],
        backgroundHeader: Image.asset('assets/images/order_bg.png',
            width: MediaQuery.of(context).size.width),
        paddingTop: MediaQuery.of(context).padding.top + kToolbarHeight + 24,
        operation: BottomOperationPanel.twoColumnOperations(
          context,
          title1: appLoc(context).makeQuote,
          onPressed1: () {},
          buttonType1: DialogButtonType.secondary,
          title2: appLoc(context).placeOrder,
          onPressed2: () {},
          buttonType2: DialogButtonType.primary,
        ));
  }

  Widget orderInfo() {
    if (_order == null) {
      return const SizedBox();
    }

    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Text(_order!.status,
          style: TextStyle(
              color: Colors.white, fontSize: 20, fontWeight: FontWeight.w500)),
      const SizedBox(height: 8),
      Text(localizedString(orderStatusSubtitles[_order!.status]!),
          style: TextStyle(color: Colors.white)),
      detail()
    ]);
  }

  Widget detail() {
    return Container(
      margin: EdgeInsets.only(top: 16),
      padding: EdgeInsets.all(10),
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(8)),
      child: Column(children: [
        Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Padding(
              padding: EdgeInsets.only(top: 4, right: 8),
              child: Image.asset(
                  truckImage(_order!.type, ignoreSelection: true),
                  width: 80,
                  fit: BoxFit.fitWidth)),
          Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Text(localizedString(_order!.type),
                style: TextStyle(fontWeight: FontWeight.w500, fontSize: 18)),
            const SizedBox(height: 4),
            Text(_order!.goods, style: TextStyle(color: Color(0xFF666666))),
            const SizedBox(height: 4),
            Row(children: [
              if (_order!.tonnage.isNotEmpty)
                Container(
                    margin: EdgeInsets.only(right: 8),
                    padding: EdgeInsets.symmetric(horizontal: 4),
                    decoration: BoxDecoration(
                        border: Border.all(color: primaryColor.withAlpha(128)),
                        borderRadius: BorderRadius.circular(4)),
                    child: Text(localizedString(_order!.tonnage.first),
                        style: TextStyle(color: primaryColor.withAlpha(128)))),
              if (_order!.volumes.isNotEmpty)
                Container(
                    margin: EdgeInsets.only(right: 8),
                    padding: EdgeInsets.symmetric(horizontal: 4),
                    decoration: BoxDecoration(
                        border: Border.all(color: primaryColor.withAlpha(128)),
                        borderRadius: BorderRadius.circular(4)),
                    child: Text(localizedString(_order!.volumes.first),
                        style: TextStyle(color: primaryColor.withAlpha(128))))
            ])
          ])
        ]),
        const SizedBox(height: 16),
        row(appLoc(context).orderDistance,
            '${(_order!.distance / 1000).toStringAsFixed(1)}km'),
        row(appLoc(context).orderLoadingAddress,
            '${_order!.origin}${_order!.pickupAddress}'),
        row(appLoc(context).orderLoadingTime,
            _order!.expectedLoadingDate.toDateString()),
        row(appLoc(context).orderUnloadingAddress,
            '${_order!.destinationCountry}${_order!.destinationCity}${_order!.destinationAddress}'),
        row(appLoc(context).orderCost, '\$ ${_order!.targetPrice}',
            valueColor: errorColor, valueWeight: FontWeight.w500),
      ]),
    );
  }

  Widget row(String title, String value,
      {Color valueColor = const Color(0xFF333333),
      FontWeight valueWeight = FontWeight.w400}) {
    return Padding(
        padding: EdgeInsets.symmetric(vertical: 10),
        child:
            Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
          Text('$title:',
              style: TextStyle(color: Color(0xFF666666), fontSize: 16)),
          SizedBox(
              width: MediaQuery.of(context).size.width / 2,
              child: Text(value,
                  textAlign: TextAlign.right,
                  style: TextStyle(
                      color: valueColor,
                      fontSize: 16,
                      fontWeight: valueWeight)))
        ]));
  }
}
