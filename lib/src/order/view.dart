import 'package:driver/src/mixins/page.dart';
import 'package:driver/src/model/firestore.dart';
import 'package:driver/src/utils/const.dart';
import 'package:driver/src/utils/convenient.dart';
import 'package:flutter/material.dart';

class OrderView extends StatefulWidget {
  const OrderView({super.key});

  @override
  State<StatefulWidget> createState() => _OrderViewState();
}

class _OrderViewState extends State<OrderView>
    with PageMixin, TickerProviderStateMixin {
  late TabController _tabController;
  List<Order> _orders = [];

  @override
  void initState() {
    _tabController = TabController(length: 3, vsync: this);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return pageBase(
        appLoc(context).orderManagement,
        _orders.isEmpty
            ? emptyIcon(type: EmptyIconType.order)
            : Column(children: [
                Container(
                    color: Colors.yellow,
                    width: MediaQuery.of(context).size.width,
                    height: 500),
                Container(
                    color: Colors.red,
                    width: MediaQuery.of(context).size.width,
                    height: 500)
              ]),
        paddingTop: 48,
        fixedContent: Positioned(
            top: 0,
            width: MediaQuery.of(context).size.width,
            child: Container(
                color: backgroundColor,
                child: TabBar(
                    controller: _tabController,
                    indicatorColor: primaryColor,
                    dividerColor: Colors.transparent,
                    unselectedLabelColor: Colors.black54,
                    labelColor: Colors.black,
                    labelStyle:
                        TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                    tabs: [
                      Tab(text: appLoc(context).orderStatusOngoing),
                      Tab(text: appLoc(context).orderStatusFinished),
                      Tab(text: appLoc(context).orderStatusCanceled)
                    ]))));
  }
}
