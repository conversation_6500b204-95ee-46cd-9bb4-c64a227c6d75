import 'package:driver/src/mixins/login.dart';
import 'package:driver/src/utils/convenient.dart';
import 'package:flutter/material.dart';

class LoginView extends StatefulWidget {
  const LoginView({super.key});

  static const String routeName = 'login';

  @override
  State<StatefulWidget> createState() => _LoginViewState();
}

class _LoginViewState extends State<LoginView> with LoginMixin {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: Stack(
      children: [
        Image.asset('assets/images/login_background.jpg',
            fit: BoxFit.cover,
            height: MediaQuery.of(context).size.height,
            width: MediaQuery.of(context).size.width),
        Padding(
            padding: EdgeInsets.symmetric(horizontal: 24),
            child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(height: MediaQuery.of(context).padding.top),
                        Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              IconButton(
                                  onPressed: () => Navigator.of(context).pop(),
                                  icon: Icon(Icons.close, size: 28))
                            ]),
                        Image.asset('assets/images/logo.png',
                            height: 48, width: 48),
                        const SizedBox(height: 10),
                        Text(appLoc(context).welcome,
                            style: TextStyle(
                                fontSize: 28, fontWeight: FontWeight.w500)),
                        Text(appLoc(context).appTitle,
                            style: TextStyle(
                                fontSize: 28, fontWeight: FontWeight.w500)),
                      ]),
                  Column(children: [platformLoginButton(), agreements()]),
                  Padding(
                      padding: EdgeInsets.only(bottom: 40),
                      child: TextButton(
                          onPressed: () {},
                          child: Text(appLoc(context).contactUs,
                              style: TextStyle(
                                  color: Color(0xFF666666), fontSize: 16))))
                ]))
      ],
    ));
  }
}
