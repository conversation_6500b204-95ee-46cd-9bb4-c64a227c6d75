import 'dart:async';

import 'package:driver/src/home/<USER>';
import 'package:driver/src/mine/view.dart';
import 'package:driver/src/order/view.dart';
import 'package:driver/src/utils/const.dart';
import 'package:driver/src/utils/convenient.dart';
import 'package:event_bus/event_bus.dart';
import 'package:flutter/material.dart';

class HomeTabsView extends StatefulWidget {
  const HomeTabsView({super.key});

  static EventBus eventBus = EventBus();

  @override
  State<StatefulWidget> createState() => _HomeTabsViewState();
}

class _HomeTabsViewState extends State<HomeTabsView> {
  int _selectedIndex = 0;
  late StreamSubscription _changeTabSubscription;

  late final List<Widget> _widgetOptions = <Widget>[
    const HomeView(),
    const OrderView(),
    const MineView(),
  ];

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  @override
  void initState() {
    super.initState();
    _changeTabSubscription =
        HomeTabsView.eventBus.on<ChangeTabEvent>().listen((e) {
      setState(() {
        _selectedIndex = e.tab;
      });
    });
  }

  @override
  void dispose() {
    _changeTabSubscription.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: IndexedStack(index: _selectedIndex, children: _widgetOptions),
      bottomNavigationBar: BottomNavigationBar(
        items: <BottomNavigationBarItem>[
          BottomNavigationBarItem(
            icon: Image.asset('assets/images/tab_icon_home_n.png',
                width: 28, height: 28),
            activeIcon: Image.asset('assets/images/tab_icon_home_s.png',
                width: 28, height: 28),
            label: appLoc(context).home,
          ),
          BottomNavigationBarItem(
            icon: Image.asset('assets/images/tab_icon_order_n.png',
                width: 28, height: 28),
            activeIcon: Image.asset('assets/images/tab_icon_order_s.png',
                width: 28, height: 28),
            label: appLoc(context).order,
          ),
          BottomNavigationBarItem(
            icon: Image.asset('assets/images/tab_icon_me_n.png',
                width: 28, height: 28),
            activeIcon: Image.asset('assets/images/tab_icon_me_s.png',
                width: 28, height: 28),
            label: appLoc(context).mine,
          ),
        ],
        currentIndex: _selectedIndex,
        selectedItemColor: primaryColor,
        unselectedItemColor: const Color(0xFF8A9899),
        selectedFontSize: 14,
        unselectedFontSize: 14,
        selectedLabelStyle: const TextStyle(fontWeight: FontWeight.w500),
        unselectedLabelStyle: const TextStyle(fontWeight: FontWeight.w400),
        backgroundColor: Colors.white,
        elevation: 0,
        useLegacyColorScheme: false,
        onTap: _onItemTapped,
      ),
    );
  }
}

class ChangeTabEvent {
  int tab;
  ChangeTabEvent(this.tab);
}
