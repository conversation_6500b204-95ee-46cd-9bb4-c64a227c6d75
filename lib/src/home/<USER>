import 'package:driver/src/home/<USER>';
import 'package:driver/src/mixins/entry.dart';
import 'package:driver/src/mixins/login.dart';
import 'package:driver/src/model/dropdown.dart';
import 'package:driver/src/model/firestore.dart';
import 'package:driver/src/model/user.dart';
import 'package:driver/src/order/detail.dart';
import 'package:driver/src/settings/controller.dart';
import 'package:driver/src/utils/const.dart';
import 'package:driver/src/utils/convenient.dart';
import 'package:driver/src/utils/firestore.dart' as fs;
import 'package:driver/src/widgets/change_notifier.dart';
import 'package:driver/src/mixins/page.dart';
import 'package:driver/src/widgets/dialog.dart';
import 'package:flutter/material.dart';
import 'package:lsapp/lsapp.dart';

class HomeView extends StatefulWidget {
  const HomeView({super.key});

  @override
  State<StatefulWidget> createState() => _HomeViewState();
}

class _HomeViewState extends State<HomeView>
    with TickerProviderStateMixin, PageMixin, EntryMixin {
  OriginLabel? _selectedOrigin;
  DestLabel? _selectedDest;
  final TextEditingController _destController = TextEditingController();
  List<CityQuote> _cityQuotes = [];
  List<Order> _orders = [];
  late final TabController _tabController;
  int _tabIndex = 0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (SettingsController.shared.isFirstLaunch) {
        SettingsController.shared.updateFirstLaunch(false);
        LoginMixin.startUserLogin(context);
      } else {
        SettingsController.shared.addListener(updateCityQuotes);
        fs.Firestore.shared
            .getOrders(orderBy: 'targetPrice', descending: true)
            .then((res) {
          setState(() {
            _orders = res;
          });
        });
      }
    });
  }

  void updateCityQuotes() {
    SettingsController controller = SettingsController.shared;
    List<String> quoteCities = controller.quoteCities;
    if (quoteCities.isNotEmpty) {
      fs.Firestore.shared
          .getLatestQuotesForCities(
              cities: quoteCities.sublist(0, controller.quoteCitiesInHomePage),
              types: controller.quoteTruckTypes.firstOrNull != null
                  ? [controller.quoteTruckTypes.first]
                  : [])
          .then((res) {
        setState(() {
          _cityQuotes = res;
        });
      });
      controller.removeListener(updateCityQuotes);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: backgroundColor,
        body: Column(mainAxisAlignment: MainAxisAlignment.start, children: [
          Stack(children: [
            Image.asset('assets/images/banner.jpg',
                width: MediaQuery.of(context).size.width, fit: BoxFit.cover),
            Positioned(
                left: 24,
                bottom: 40,
                child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(appLoc(context).use,
                          style: TextStyle(
                              fontSize: 28, fontWeight: FontWeight.w600)),
                      Text(appLoc(context).appTitle,
                          style: TextStyle(
                              fontSize: 14, fontWeight: FontWeight.w600))
                    ]))
          ]),
          locationPanel(),
          scrollPanel()
        ]));
  }

  Widget locationPanel() {
    return Container(
      width: MediaQuery.of(context).size.width - 32,
      transform: Matrix4.translationValues(0.0, -16.0, 0.0),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(4),
          boxShadow: [
            BoxShadow(
                color: Color(0x1F000000),
                blurRadius: 16,
                spreadRadius: -6,
                offset: Offset(0, 6))
          ]),
      padding: EdgeInsets.all(10),
      child: settingProvider((controller) =>
          Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            locationSelector(originLabels(controller), (e) {
              if (e != null && e is OriginLabel) {
                setState(() {
                  _selectedOrigin = e;
                });
              }
            }, initialSelection: _selectedOrigin),
            locationSelector(destLabels(controller), (e) {
              setState(() {
                _selectedDest = e as DestLabel?;
              });
            },
                hintText: appLoc(context).selectDestinationCountry,
                dotColor: errorColor,
                fillColor:
                    _selectedDest == null ? Color(0x1A029FB0) : Colors.white,
                controller: _destController,
                trailingIcon: _selectedDest != null
                    ? InkWell(
                        onTap: () {
                          setState(() {
                            _destController.text = '';
                            _selectedDest = null;
                          });
                        },
                        child: Icon(Icons.clear, size: 16))
                    : null),
            truckSelector()
          ])),
    );
  }

  Widget truckSelector() {
    return userProvider((user) {
      void Function() onTap;
      List<TextSpan> texts = [];
      TruckVerification? truck = user.defaultTruck();
      if (truck == null) {
        onTap = () => LoginMixin.startUserLogin(context);
        texts = [
          TextSpan(text: appLoc(context).loadingTruckNotRegistered),
          TextSpan(
              text: appLoc(context).registerTruck,
              style: TextStyle(
                decoration: TextDecoration.underline,
              )),
        ];
      } else {
        onTap = () async {
          if (user.truckVerifications.length > 1) {
            final selected = await showSelectionDialog(
                options: user.truckVerifications,
                title: appLoc(context).selectTruck,
                cancelText: appLoc(context).cancel,
                initialSelectIndex:
                    SettingsController.shared.defaultTruckIndex);
            if (selected != null) {
              user.updateDefaultTruck(selected);
            }
          }
        };
        texts = [
          TextSpan(
              text: '${appLoc(context).license}${truck.license} ${truck.type}'),
          if (user.truckVerifications.length > 1)
            TextSpan(
                text: appLoc(context).switchTruck,
                style: TextStyle(color: primaryColor)),
        ];
      }
      return Padding(
          padding: EdgeInsets.only(left: 16, top: 8, bottom: 8),
          child: InkWell(
              onTap: onTap,
              child: Text.rich(
                textAlign: TextAlign.start,
                TextSpan(
                  text: appLoc(context).loadingTruck,
                  style: TextStyle(fontSize: 14, color: Color(0xFF666666)),
                  children: texts,
                ),
              )));
    });
  }

  Widget quotesPanel() {
    if (_cityQuotes.isEmpty) {
      return const SizedBox();
    }

    return userProvider((user) => InkWell(
        onTap: () {
          if (user.driverVerification != null) {
            Navigator.of(context).pushNamed(QuoteCenterView.routeName,
                arguments: _selectedOrigin?.city);
          }
        },
        child: Container(
          width: MediaQuery.of(context).size.width - 32,
          margin: EdgeInsets.only(bottom: 10),
          decoration: BoxDecoration(
              color: Colors.white, borderRadius: BorderRadius.circular(4)),
          child: Column(children: [
            Container(
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                    gradient: LinearGradient(
                        colors: [Color(0xFFEEFDFF), Color(0xFFFFFFFF)])),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(appLoc(context).quoteCenter,
                        style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: Color(0xFF333333))),
                    Row(children: [
                      Text(
                          appLoc(context)
                              .quoteFrom(_selectedOrigin?.city ?? ''),
                          style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w400,
                              color: Color(0xFF666666))),
                      Padding(
                          padding: EdgeInsets.only(top: 2),
                          child: Icon(Icons.arrow_forward_ios,
                              size: 12, color: Color(0xFF666666)))
                    ])
                  ],
                )),
            Padding(
                padding: EdgeInsets.symmetric(vertical: 16),
                child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: _cityQuotes
                        .map((e) => Column(children: [
                              Row(children: [
                                Text(e.destination,
                                    style: TextStyle(
                                        color: Color(0xFF333333),
                                        fontSize: 14)),
                                if (e.delta != 0)
                                  Padding(
                                      padding: EdgeInsets.only(left: 2),
                                      child: Image.asset(
                                          e.delta > 0
                                              ? 'assets/images/up.png'
                                              : 'assets/images/down.png',
                                          width: 10))
                              ]),
                              Row(children: [
                                Text('\$',
                                    style: TextStyle(
                                        color: errorColor,
                                        fontSize: 14,
                                        fontWeight: FontWeight.w400)),
                                const SizedBox(width: 1),
                                Text(maskPrice(user, e.quotation),
                                    style: TextStyle(
                                        color: errorColor,
                                        fontSize: 18,
                                        fontWeight: FontWeight.w500)),
                                SizedBox(width: e.delta != 0 ? 12 : 0),
                              ])
                            ]))
                        .toList()))
          ]),
        )));
  }

  Widget orderCard(User user, Order order) {
    return Container(
        width: MediaQuery.of(context).size.width - 32,
        decoration: BoxDecoration(
            color: Colors.white, borderRadius: BorderRadius.circular(8)),
        child: Column(children: [
          Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                  border: Border(bottom: BorderSide(color: Color(0xFFE1E3E6)))),
              child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                        '${appLoc(context).pickupDate} ${order.expectedLoadingDate.toDateString()}',
                        style: TextStyle(
                            color: Color(0xFF333333),
                            fontSize: 14,
                            fontWeight: FontWeight.w500)),
                    Text(
                        appLoc(context).distance(
                            (order.distance / 1000).toStringAsFixed(1)),
                        style: TextStyle(
                            color: Color(0xFF999999),
                            fontSize: 14,
                            fontWeight: FontWeight.w400))
                  ])),
          Container(
            padding: EdgeInsets.all(12),
            child:
                Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              Row(crossAxisAlignment: CrossAxisAlignment.center, children: [
                Container(
                  width: 6,
                  height: 6,
                  decoration: BoxDecoration(
                      color: primaryColor,
                      borderRadius: BorderRadius.circular(6)),
                ),
                const SizedBox(width: 8),
                Text(
                    '${appLoc(context).loading}: ${localizedString(order.origin)}',
                    style: TextStyle(
                        color: Color(0xFF333333),
                        fontSize: 14,
                        fontWeight: FontWeight.w500))
              ]),
              Padding(
                  padding: EdgeInsets.only(left: 14),
                  child: Text(order.pickupAddress,
                      style: TextStyle(
                          color: Color(0xFF666666),
                          fontSize: 12,
                          fontWeight: FontWeight.w400)))
            ]),
          ),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 12),
            child:
                Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              Row(crossAxisAlignment: CrossAxisAlignment.center, children: [
                Container(
                  width: 6,
                  height: 6,
                  decoration: BoxDecoration(
                      color: errorColor,
                      borderRadius: BorderRadius.circular(6)),
                ),
                const SizedBox(width: 8),
                Text(
                    '${appLoc(context).unloading}: ${localizedString(order.destinationCountry)} - ${localizedString(order.destinationCity)}',
                    style: TextStyle(
                        color: Color(0xFF333333),
                        fontSize: 14,
                        fontWeight: FontWeight.w500))
              ]),
              Padding(
                  padding: EdgeInsets.only(left: 14),
                  child: Text(order.destinationAddress,
                      style: TextStyle(
                          color: Color(0xFF666666),
                          fontSize: 12,
                          fontWeight: FontWeight.w400)))
            ]),
          ),
          const SizedBox(height: 12),
          Row(children: [
            Container(
                margin: EdgeInsets.only(left: 12, right: 8),
                padding: EdgeInsets.symmetric(horizontal: 4),
                decoration: BoxDecoration(
                    border: Border.all(color: primaryColor.withAlpha(128)),
                    borderRadius: BorderRadius.circular(4)),
                child: Text(localizedString(order.type),
                    style: TextStyle(color: primaryColor.withAlpha(128)))),
            if (order.tonnage.isNotEmpty)
              Container(
                  margin: EdgeInsets.only(right: 8),
                  padding: EdgeInsets.symmetric(horizontal: 4),
                  decoration: BoxDecoration(
                      border: Border.all(color: primaryColor.withAlpha(128)),
                      borderRadius: BorderRadius.circular(4)),
                  child: Text(localizedString(order.tonnage.first),
                      style: TextStyle(color: primaryColor.withAlpha(128)))),
            if (order.volumes.isNotEmpty)
              Container(
                  margin: EdgeInsets.only(right: 8),
                  padding: EdgeInsets.symmetric(horizontal: 4),
                  decoration: BoxDecoration(
                      border: Border.all(color: primaryColor.withAlpha(128)),
                      borderRadius: BorderRadius.circular(4)),
                  child: Text(localizedString(order.volumes.first),
                      style: TextStyle(color: primaryColor.withAlpha(128))))
          ]),
          Container(
              margin: EdgeInsets.only(top: 12),
              width: double.infinity,
              height: 1,
              color: Color(0xFFE1E3E6)),
          Container(
              padding: EdgeInsets.all(12),
              child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(children: [
                      Text('\$',
                          style: TextStyle(
                              color: errorColor,
                              fontSize: 14,
                              fontWeight: FontWeight.w400)),
                      const SizedBox(width: 1),
                      Text(maskPrice(user, order.targetPrice),
                          style: TextStyle(
                              color: errorColor,
                              fontSize: 20,
                              fontWeight: FontWeight.w500))
                    ]),
                    ElevatedButton(
                        onPressed: () => viewOrder(user, order),
                        style: ElevatedButton.styleFrom(
                            elevation: 0,
                            backgroundColor: primaryColor.withAlpha(20),
                            foregroundColor: primaryColor,
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8))),
                        child: Text(appLoc(context).viewOrder))
                  ]))
        ]));
  }

  Future viewOrder(User user, Order order) async {
    if (user.canViewOrder()) {
      Navigator.of(context)
          .pushNamed(OrderDetailView.routeName, arguments: order);
    } else {
      String message;
      String title;
      String primaryTitle;
      Function function;
      startLogin() => LoginMixin.startUserLogin(context);
      customerService() {}

      if (user.driverVerification == null) {
        title = appLoc(context).notVerifiedDriverTitle;
        message = appLoc(context).notVerifiedDriverMessage;
        primaryTitle = appLoc(context).goVerify;
        function = startLogin;
      } else if (user.driverVerification?.status ==
              VerificationStatus.pending ||
          user.driverVerification?.status == VerificationStatus.rejected) {
        title = appLoc(context).verifyingDriverTitle;
        message = appLoc(context).verifyingDriverMessage;
        primaryTitle = appLoc(context).customerService;
        function = customerService;
      } else if (user.truckVerifications
          .map((e) => e.status == VerificationStatus.pending)
          .isNotEmpty) {
        title = appLoc(context).verifyingTruckTitle;
        message = appLoc(context).verifyingTruckMessage;
        primaryTitle = appLoc(context).customerService;
        function = customerService;
      } else {
        title = appLoc(context).notVerifiedTruckTitle;
        message = appLoc(context).notVerifiedTruckMessage;
        primaryTitle = appLoc(context).goVerify;
        function = startLogin;
      }

      DialogButtonType? ret = await showAppDialog(
          context,
          dialogMessage(message: message),
          [
            dialogButton(
                context, appLoc(context).notYet, DialogButtonType.cancel),
            dialogButton(context, primaryTitle, DialogButtonType.primary)
          ],
          title: title);
      if (ret == DialogButtonType.primary && mounted) {
        function();
      }
    }
  }

  Widget orderList() {
    return Column(
      children: [
        TabBar(
          controller: _tabController,
          onTap: (i) {
            setState(() {
              _tabIndex = i;
            });
          },
          isScrollable: true,
          indicatorColor: primaryColor,
          indicatorSize: TabBarIndicatorSize.label,
          dividerColor: Colors.transparent,
          labelColor: primaryColor,
          unselectedLabelColor: Color(0xFF666666),
          tabAlignment: TabAlignment.start,
          tabs: <Widget>[
            Tab(text: appLoc(context).rankByPrice),
            Tab(text: appLoc(context).rankByPref),
          ],
        ),
        const SizedBox(height: 10),
        _tabIndex == 1
            ? Container(
                width: MediaQuery.of(context).size.width - 64,
                padding: EdgeInsets.all(10),
                decoration: BoxDecoration(
                    color: Color(0x0DFF6802),
                    borderRadius: BorderRadius.circular(4)),
                child: Row(
                  children: [
                    Icon(Icons.info, color: Color(0xFFFF6802), size: 20),
                    const SizedBox(width: 8),
                    Expanded(child: Text(appLoc(context).rankByPrefNotice))
                  ],
                ),
              )
            : _orders.isEmpty
                ? emptyIcon(type: EmptyIconType.order, paddingTop: 40)
                : userProvider((user) => Column(
                    spacing: 10,
                    children: _orders.map((e) => orderCard(user, e)).toList()))
      ],
    );
  }

  Widget scrollPanel() {
    return Expanded(
        child: SizedBox(
      width: MediaQuery.of(context).size.width - 32,
      child: SingleChildScrollView(
          padding: EdgeInsets.only(bottom: 20),
          child: Column(
            children: [quotesPanel(), orderList()],
          )),
    ));
  }

  List<OriginLabel> originLabels(SettingsController controller) {
    List<OriginLabel> labels = controller.origins
        .map((e) => OriginLabel(e['country']!, e['city']!))
        .toList();
    _selectedOrigin = labels.firstOrNull;
    return labels;
  }

  List<DestLabel> destLabels(SettingsController controller) {
    List<DestLabel> labels =
        controller.destinationCountries.map((e) => DestLabel(e)).toList();
    return labels;
  }

  Widget locationSelector(List<SelectorLabel> labels,
      final ValueChanged<SelectorLabel?>? onSelected,
      {SelectorLabel? initialSelection,
      String? hintText,
      Color dotColor = primaryColor,
      Color fillColor = Colors.white,
      TextEditingController? controller,
      Widget? trailingIcon}) {
    List<DropdownMenuEntry<SelectorLabel>> entries = labels
        .map((label) => DropdownMenuEntry<SelectorLabel>(
            value: label,
            label: label.label,
            style: MenuItemButton.styleFrom(backgroundColor: Colors.white)))
        .toList();
    return dropDown(entries,
        leadingIcon: Icon(Icons.circle, color: dotColor, size: 16),
        width: MediaQuery.of(context).size.width - 52,
        hintText: hintText,
        controller: controller,
        trailingIcon: trailingIcon,
        initialSelection: initialSelection,
        onSelected: onSelected,
        textStyle: TextStyle(color: Color(0xFF333333), fontSize: 16),
        inputDecorationTheme: InputDecorationTheme(
            fillColor: fillColor,
            filled: true,
            outlineBorder: BorderSide.none,
            border: InputBorder.none,
            hintStyle: TextStyle(color: Color(0xFF666666))));
  }
}
