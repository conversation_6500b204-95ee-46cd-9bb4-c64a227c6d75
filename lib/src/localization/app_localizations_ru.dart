import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Russian (`ru`).
class AppLocalizationsRu extends AppLocalizations {
  AppLocalizationsRu([String locale = 'ru']) : super(locale);

  @override
  String get appTitle => '马士拿国际货运';

  @override
  String get welcome => '欢迎来到';

  @override
  String get use => '欢迎使用';

  @override
  String get googleLogin => '谷歌 RU Login';

  @override
  String get appleLogin => '苹果 RU Login';

  @override
  String get agreementText => '我已阅读并同意';

  @override
  String get userAgreement => '《马士拿软件用户协议》、';

  @override
  String get privacyAgreement => '《马士拿软件隐私协议》、';

  @override
  String get infoAgreement => '《马士拿信息承诺协议》';

  @override
  String get contactUs => '遇见问题，联系我们';

  @override
  String get notAgreeMessage1 => '检测到您尚未详细阅读协议，请您仔细阅读并同意';

  @override
  String get notAgreeMessage2 => '，请点击“同意”开始使用我们的产品和服务。';

  @override
  String get cancel => '取消';

  @override
  String get agree => '同意';

  @override
  String get readAgreement => '阅读协议';

  @override
  String get home => '首页';

  @override
  String get order => '订单';

  @override
  String get mine => '我的';

  @override
  String get networkError => '网络异常，请稍后再试';

  @override
  String loadFrom(Object origin) {
    return '从 $origin 装车';
  }

  @override
  String get selectDestinationCountry => '请选择你准备要去的国家';

  @override
  String get loadingTruck => '运载车辆: ';

  @override
  String get loadingTruckNotRegistered => '您尚未维护车辆信息，去';

  @override
  String get registerTruck => '完善车辆信息';

  @override
  String get quoteCenter => '行情中心';

  @override
  String quoteFrom(Object origin) {
    return '$origin出发';
  }

  @override
  String get rankByPrice => '高价排序';

  @override
  String get rankByPref => '偏好排序';

  @override
  String get pickupDate => '取货日期:';

  @override
  String distance(Object distance) {
    return '全程${distance}km';
  }

  @override
  String get loading => '装车';

  @override
  String get unloading => '卸货';

  @override
  String get noOrder => '暂无订单';

  @override
  String get noData => '暂无数据';

  @override
  String get viewOrder => '查看订单';

  @override
  String get rankByPrefNotice => '您需要先在上方维护装车卸车地及运载车辆，才可以设置偏好';

  @override
  String get login => '登录';

  @override
  String get verifyTitle => '认证平台司机，快速接单';

  @override
  String get verifyAction => '前往认证';

  @override
  String get massiveOrders => '大量订单';

  @override
  String get fastPay => '及时结算';

  @override
  String get safe => '平台保障';

  @override
  String get driverCenter => '司机中心';

  @override
  String get driverVerify => '司机认证';

  @override
  String get truckManagement => '车辆管理';

  @override
  String get bankInfo => '银行信息';

  @override
  String get serviceCenter => '服务中心';

  @override
  String get customerService => '联系客服';

  @override
  String get guarantee => '信息承诺协议';

  @override
  String get userAgreementAction => '用户协议';

  @override
  String get privacyAgreementAction => '隐私协议';

  @override
  String get switchLanguage => '切换语言';

  @override
  String get completeInfo => '请完善信息';

  @override
  String get userInfo => '个人信息';

  @override
  String get phone => '电话';

  @override
  String get phonePlaceholder => '请输入您的电话';

  @override
  String get driverName => '司机姓名（英语）';

  @override
  String get driverNamePlaceholder => '请输入您的姓名';

  @override
  String get passportPhoto => '护照照片';

  @override
  String get moreContactInfo => '更多联系方式';

  @override
  String get chinesePhone => '中国电话';

  @override
  String get chinesePhonePlaceholder => '请输入您的中国电话';

  @override
  String get wechat => '微信号';

  @override
  String get wechatPlaceholder => '请输入您的微信号';

  @override
  String get save => '保存';

  @override
  String get quickEntry => '快速输入';

  @override
  String get quickEntryMessage => '根据您填写的内容，匹配到如下信息，您可以选择正确信息快速填入';

  @override
  String get confirmSelect => '确认选择';

  @override
  String get cancelSelect => '都不是';

  @override
  String get imageFromAlbum => '从相册选择';

  @override
  String get imageFromCamera => '拍照';

  @override
  String get allQuoteTruckTypes => '全部车型';

  @override
  String get allQuoteAreas => '全部地区';

  @override
  String get latestQuote => '最新报价';

  @override
  String get quoteDelta => '日涨跌比';

  @override
  String get quoteDetail => '行情详情';

  @override
  String get quoteHistory => '历史报价';

  @override
  String get quoteTime => '报价时间';

  @override
  String get quotePrice => '价格';

  @override
  String get waitForDriverVerificationTitle => '提交审核';

  @override
  String get waitForDriverVerificationMessage => '您已成功提交审核，我们将会在3个工作日内审核完毕，期间请保持电话畅通。是否进行车辆认证？';

  @override
  String get waitForTruckVerificationMessage => '您已成功提交审核，我们将会在3个工作日内审核完毕，期间请保持电话畅通，审核通过后可立即接单。';

  @override
  String get notYet => '先不了';

  @override
  String get goVerify => '立即认证';

  @override
  String get understand => '知道了';

  @override
  String get noTruck => '暂无车辆，去';

  @override
  String get add => '新增';

  @override
  String get addTruckTitle => '新增车辆';

  @override
  String get license => '车牌号';

  @override
  String get licensePlaceholder => '请输入车牌号';

  @override
  String get licenseImage => '车辆行驶证';

  @override
  String get next => '下一步';

  @override
  String get truckType => '车辆类型';

  @override
  String get tonnage => '车载吨位';

  @override
  String get tonnagePlaceholder => '请选择吨位';

  @override
  String get volume => '车载体积';

  @override
  String get volumePlaceholder => '请选择体积';

  @override
  String get confirm => '确定';

  @override
  String get verified => '已认证';

  @override
  String get verificationEmpty => '待认证';

  @override
  String get verificationPending => '认证中';

  @override
  String get verificationRejected => '认证失败';

  @override
  String get verificationRejectedReason => '请联系客服';

  @override
  String get deleteTruckMessage => '您正在删除关联车辆，确认删除吗？';

  @override
  String get confirmDelete => '确认删除';

  @override
  String get cancelDelete => '不删除';

  @override
  String get noBank => '暂无银行信息，去';

  @override
  String get addBankTitle => '新增银行信息';

  @override
  String get selectBankType => '选择账户类型';

  @override
  String get individual => '个人账户';

  @override
  String get business => '个体工商户';

  @override
  String get individualName => '姓名';

  @override
  String get individualNamePlaceholder => '请输入个人姓名';

  @override
  String get bankName => '开户行名称';

  @override
  String get bankNamePlaceholder => '请输入开户行名称';

  @override
  String get bankAccount => '账号';

  @override
  String get bankAccountPlaceholder => '请输入账号';

  @override
  String get submit => '提交';

  @override
  String get swiftAccount => 'KZT SWIFT 代码 BIC';

  @override
  String get swiftAccountPlaceholder => '请输入KZT SWIFT 代码 BIC';

  @override
  String get businessOwnerName => '姓名';

  @override
  String get businessOwnerNamePlaceholder => '请输入个体工商户姓名';

  @override
  String get taxNumber => '税号';

  @override
  String get taxNumberPlaceholder => '请输入税号';

  @override
  String get businessAddress => '注册地址';

  @override
  String get businessAddressPlaceholder => '请输入个体工商户注册地址';

  @override
  String get beneficiaryCode => '受益人代码';

  @override
  String get beneficiaryCodePlaceholder => '请输入受益人代码';

  @override
  String get usageCode => '付款用途代码';

  @override
  String get usageCodePlaceholder => '请输入付款用途代码';

  @override
  String get deleteBankMessage => '您正在删除银行信息，确认删除吗？';

  @override
  String get submitSuccess => '提交成功';

  @override
  String get switchTruck => '切换';

  @override
  String get loadingImage => '正在下载图片';

  @override
  String get selectTruck => '选择车辆';

  @override
  String get uid => '用户编号';

  @override
  String get copyToClipboard => '已复制到剪贴板';

  @override
  String get preventTruckDeleteTitle => '车辆信息认证中';

  @override
  String get preventTruckDelete => '车辆信息正在认证，请稍后再操作，或者联系客服加速处理。';

  @override
  String get contactForVerification => '联系客服加速认证';

  @override
  String get notVerifiedDriverTitle => '尚未认证';

  @override
  String get notVerifiedDriverMessage => '您的资料不完善，目前不能接单，请及时去完善您的资料，立即开启接单';

  @override
  String get notVerifiedTruckTitle => '暂无车辆';

  @override
  String get notVerifiedTruckMessage => '您尚未维护车辆信息，目前不能接单，请及时去完善车辆信息，立即开启接单';

  @override
  String get verifyingDriverTitle => '尚未认证';

  @override
  String get verifyingDriverMessage => '您的资料不完善，目前不能接单，请及时去完善您的资料，立即开启接单';

  @override
  String get verifyingTruckTitle => '暂无车辆';

  @override
  String get verifyingTruckMessage => '您尚未维护车辆信息，目前不能接单，请及时去完善车辆信息，立即开启接单';

  @override
  String get orderDetail => '订单详情';

  @override
  String get orderDistance => '订单全程距离';

  @override
  String get orderLoadingAddress => '装货地点';

  @override
  String get orderUnloadingAddress => '卸货地点';

  @override
  String get orderLoadingTime => '装货日期';

  @override
  String get orderCost => '运费';

  @override
  String get makeQuote => '报价协商';

  @override
  String get placeOrder => '立即接单';

  @override
  String get orderManagement => '订单管理';

  @override
  String get orderStatusOngoing => '进行中';

  @override
  String get orderStatusFinished => '已完成';

  @override
  String get orderStatusCanceled => '已取消';
}
