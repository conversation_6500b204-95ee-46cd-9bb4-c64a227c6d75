import 'package:driver/src/model/firestore.dart';
import 'package:driver/src/settings/controller.dart';
import 'package:driver/src/utils/const.dart';
import 'package:driver/src/utils/convenient.dart';
import 'package:flutter/material.dart';

mixin QuoteMixin<T extends StatefulWidget> on State<T> {
  Widget quotePrice(CityQuote quote) {
    return Row(
      children: [
        Text('\$', style: TextStyle(fontSize: 12)),
        Text('${quote.quotation}', style: TextStyle(fontSize: 16))
      ],
    );
  }

  Widget quoteDelta(CityQuote quote) {
    return Wrap(children: [
      Container(
          padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
          width: 76,
          alignment: Alignment.center,
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(2),
              color: quote.delta > 0
                  ? errorColor
                  : quote.delta == 0
                      ? Colors.grey
                      : primaryColor),
          child: Text(
            '${quote.delta > 0 ? '+' : ''}${(quote.delta * 100).toStringAsFixed(2)}%',
            style: TextStyle(color: Colors.white),
          ))
    ]);
  }

  Widget quoteCard(CityQuote quote, {void Function()? onTap}) {
    return InkWell(
        onTap: onTap,
        child: Container(
          padding: EdgeInsets.all(10),
          width: MediaQuery.of(context).size.width - 32,
          decoration: BoxDecoration(
              color: Colors.white, borderRadius: BorderRadius.circular(4)),
          child: Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Padding(
                padding: EdgeInsets.only(top: 4, right: 12),
                child: Image.asset(truckImage(quote.type),
                    width: 80, fit: BoxFit.fitWidth)),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                    '${localizedString(SettingsController.shared.origins.firstOrNull?['city'] ?? '')} - ${localizedString(quote.destination)}',
                    style: TextStyle(fontSize: 16)),
                Text(quote.typeName,
                    style: TextStyle(color: Color(0xFF666666))),
                const SizedBox(height: 12),
                Row(children: [
                  Text(appLoc(context).latestQuote,
                      style: TextStyle(color: Color(0xFF999999))),
                  const SizedBox(width: 6),
                  quotePrice(quote),
                  const SizedBox(width: 32),
                  Text(appLoc(context).quoteDelta,
                      style: TextStyle(color: Color(0xFF999999))),
                  const SizedBox(width: 6),
                  quoteDelta(quote)
                ])
              ],
            )
          ]),
        ));
  }
}
