import 'package:driver/src/mixins/entry.dart';
import 'package:driver/src/model/firestore.dart';
import 'package:driver/src/model/user.dart';
import 'package:driver/src/utils/const.dart';
import 'package:driver/src/utils/convenient.dart';
import 'package:driver/src/mixins/page.dart';
import 'package:driver/src/widgets/change_notifier.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:lsapp/lsapp.dart';

class DriverInfoView extends StatefulWidget {
  const DriverInfoView({super.key});

  static const String routeName = 'driver_info';

  @override
  State<StatefulWidget> createState() => _DriverInfoViewState();
}

class _DriverInfoViewState extends State<DriverInfoView>
    with EntryMixin, PageMixin {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      User.shared.syncDriverVerification();
    });
  }

  @override
  Widget build(BuildContext context) {
    return userProvider((user) {
      DriverVerification? driverVerification = user.driverVerification;
      String? unverifiedStatus = user.unverifiedDriverStatus();
      if (driverVerification == null) {
        return SizedBox();
      }

      return pageBase(
          appLoc(context).userInfo,
          Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            item(appLoc(context).phone, driverVerification.phone),
            item(appLoc(context).driverName, driverVerification.name),
            passport(driverVerification.passport),
            if (driverVerification.chinesePhone?.isNotEmpty == true ||
                driverVerification.wechat?.isNotEmpty == true)
              Padding(
                  padding: EdgeInsets.only(bottom: 10),
                  child: Text(appLoc(context).moreContactInfo,
                      style: TextStyle(color: Color(0xFF666666)))),
            item(appLoc(context).chinesePhone, driverVerification.chinesePhone),
            item(appLoc(context).wechat, driverVerification.wechat),
            item(appLoc(context).uid, driverVerification.uid, onTap: () async {
              await Clipboard.setData(
                  ClipboardData(text: driverVerification.uid));
              if (context.mounted) {
                showSnackBar(context, text: appLoc(context).copyToClipboard);
              }
            },
                trailing: Padding(
                    padding: EdgeInsets.only(left: 8),
                    child: Icon(Icons.copy, size: 16)))
          ]),
          appBarBottom: unverifiedStatus != null
              ? PreferredSize(
                  preferredSize: Size(
                      MediaQuery.of(context).size.width,
                      driverVerification.status == VerificationStatus.pending
                          ? 56
                          : 88),
                  child: Container(
                      padding: EdgeInsets.all(16),
                      child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(children: [
                              driverVerification.status ==
                                      VerificationStatus.pending
                                  ? Icon(Icons.access_time_filled,
                                      size: 18, color: warningColor)
                                  : Icon(Icons.info,
                                      size: 18, color: errorColor),
                              const SizedBox(width: 8),
                              Text(unverifiedStatus,
                                  style: TextStyle(
                                      color: Color(0xFF333333), fontSize: 16))
                            ]),
                            if (driverVerification.status ==
                                VerificationStatus.rejected)
                              Padding(
                                  padding: EdgeInsets.only(left: 28, top: 8),
                                  child: Text(
                                      driverVerification.reason ??
                                          appLoc(context)
                                              .verificationRejectedReason,
                                      style: TextStyle(
                                          color: Color(0xFF666666),
                                          fontSize: 16)))
                          ])))
              : null,
          operation: driverVerification.status == VerificationStatus.rejected
              ? BottomOperationPanel.singleOperation(
                  context, appLoc(context).customerService, () {})
              : null);
    });
  }

  Widget item(String title, String? value,
      {Widget? trailing, void Function()? onTap}) {
    if (value?.isEmpty == true) {
      return SizedBox();
    }
    return formItem(
        title: title,
        input: InkWell(
            onTap: onTap,
            focusColor: Colors.transparent,
            splashFactory: NoSplash.splashFactory,
            child: Padding(
                padding: EdgeInsets.only(bottom: 10, top: 10),
                child: Row(children: [
                  Text(value!),
                  if (trailing != null) trailing
                ]))));
  }

  Widget passport(String path) {
    return formItem(
        title: appLoc(context).passportPhoto, input: storageImage(path));
  }
}
