import 'dart:async';
import 'package:driver/src/app.dart';
import 'package:driver/src/model/firestore.dart';
import 'package:driver/src/settings/controller.dart';
import 'package:driver/src/utils/convenient.dart';
import 'package:driver/src/utils/functions.dart';
import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart' as auth;
import 'package:lsapp/lsapp.dart';
import 'package:driver/src/utils/firestore.dart' as fs;

class User extends ChangeNotifier {
  static final User _shared = User();
  static User get shared => _shared;

  late auth.User? user;

  String? fcmToken;
  static const String appleAuthProviderId = 'apple.com';
  static const String emailAuthProviderId = 'password';

  DriverVerification? driverVerification;
  List<TruckVerification> truckVerifications = [];

  Future init() async {
    user = auth.FirebaseAuth.instance.currentUser;
    updateUserInfo();

    auth.FirebaseAuth.instance.userChanges().listen((u) async {
      debugPrint('userChanges $u');
      bool shouldUpdate = false;
      if (user?.uid != u?.uid) {
        shouldUpdate = true;
      }
      user = u;
      debugPrint('shouldUpdate $shouldUpdate');
      if (shouldUpdate) {
        updateUserInfo();
      }
    });
  }

  Future updateUserInfo() async {
    if (hasRegistered()) {
      await updateAccountInfo();
      await registerFcmToken();
    }
  }

  Future reload() async {
    await auth.FirebaseAuth.instance.currentUser?.reload();
  }

  void updateDriverVerification(Map<String, dynamic>? driverVerificationData) {
    if (driverVerificationData != null) {
      driverVerification = DriverVerification.fromMap(driverVerificationData);
      notifyListeners();
    }
  }

  void updateTruckVerification(Map<String, dynamic>? truckVerificationData) {
    if (truckVerificationData != null) {
      truckVerifications.add(TruckVerification.fromMap(truckVerificationData));
      notifyListeners();
    }
  }

  Future<bool> removeTruckVerification(
      TruckVerification truckVerification) async {
    if (await Functions.shared.deleteTruckVerification(truckVerification)) {
      truckVerifications.remove(truckVerification);
      SettingsController.shared.updateDefaultTruckIndex(0);
      notifyListeners();
      return true;
    }
    return false;
  }

  Future syncDriverVerification() async {
    if (hasRegistered()) {
      updateDriverVerification(
          await fs.Firestore.shared.getDriverVerification());
    }
  }

  Future syncTruckVerifications() async {
    if (hasRegistered()) {
      List<Map<String, dynamic>> res =
          await fs.Firestore.shared.getTruckVerifications();
      truckVerifications = [];
      if (res.isNotEmpty) {
        for (final r in res) {
          updateTruckVerification(r);
        }
      }
    }
  }

  Future updateAccountInfo() async {
    debugPrint('updateAccountInfo');
    await Firestore.shared.updateUser(user!.uid, {
      'email': user!.email,
      'locale': appLoc(MyApp.materialKey.currentContext!).localeName
    });
    syncDriverVerification();
    syncTruckVerifications();
    notifyListeners();
  }

  String? unverifiedDriverStatus() {
    if (driverVerification == null) {
      return appLoc(MyApp.materialKey.currentContext!).verificationEmpty;
    }
    if (driverVerification?.status == VerificationStatus.verified) {
      return null;
    }
    return driverVerification?.status == VerificationStatus.pending
        ? appLoc(MyApp.materialKey.currentContext!).verificationPending
        : appLoc(MyApp.materialKey.currentContext!).verificationRejected;
  }

  TruckVerification? defaultTruck() {
    return truckVerifications.length >
            SettingsController.shared.defaultTruckIndex
        ? truckVerifications[SettingsController.shared.defaultTruckIndex]
        : null;
  }

  void updateDefaultTruck(TruckVerification truck) {
    SettingsController.shared
        .updateDefaultTruckIndex(truckVerifications.indexOf(truck));
    notifyListeners();
  }

  bool hasRegistered() {
    return user?.uid != null;
  }

  bool canViewOrder() {
    return driverVerification?.status == VerificationStatus.verified &&
        truckVerifications
            .map((e) => e.status == VerificationStatus.verified)
            .isNotEmpty;
  }

  Future registerFcmToken() async {
    if (fcmToken != null && hasRegistered()) {
      Firestore.shared.updateMessagingToken(fcmToken!, user!.uid);
    }
  }

  Future logout() async {
    await auth.FirebaseAuth.instance.signOut();
    await signOutGoogle();
  }

  bool loggingOut = false;

  Future<bool> performLogout() async {
    if (loggingOut) {
      return false;
    }
    loggingOut = true;
    Loader.show();

    await User.shared.logout();
    Loader.hide();
    loggingOut = false;
    return true;
  }
}
