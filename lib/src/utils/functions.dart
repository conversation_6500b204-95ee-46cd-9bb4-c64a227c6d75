import 'package:cloud_functions/cloud_functions.dart';
import 'package:driver/src/model/firestore.dart';
import 'package:driver/src/model/user.dart';
import 'package:flutter/widgets.dart';

class Functions {
  static Functions shared = Functions();

  Future<List<Driver>> findDriversWithPhoneNumber(String phone) async {
    try {
      final result = await FirebaseFunctions.instance
          .httpsCallable('findDriversWithPhoneNumber')
          .call(
        {"phone": phone},
      );
      if (result.data is List && result.data.isNotEmpty) {
        return List<Driver>.from(result.data
            .map((e) => Driver.fromMap(Map<String, dynamic>.from(e))));
      }
    } catch (e) {
      debugPrint('$e');
    }
    return [];
  }

  Future<List<Truck>> findTrucksWithLicense(String license) async {
    try {
      final result = await FirebaseFunctions.instance
          .httpsCallable('findTruckWithLicense')
          .call(
        {"license": license},
      );
      if (result.data is List && result.data.isNotEmpty) {
        return List<Truck>.from(result.data
            .map((e) => Truck.fromMap(Map<String, dynamic>.from(e))));
      }
    } catch (e) {
      debugPrint('$e');
    }
    return [];
  }

  Future<String?> getStorageImage(
      String id, String token, String recordId, String funcName) async {
    if (token.isEmpty) return null;
    try {
      final result =
          await FirebaseFunctions.instance.httpsCallable(funcName).call(
        {"id": id, "token": token, "recordId": recordId},
      );
      return result.data['path'] as String?;
    } catch (e) {
      debugPrint('$e');
    }

    return null;
  }

  Future<bool> deleteTruckVerification(TruckVerification truck) async {
    if (User.shared.user?.uid == truck.uid && truck.recordId != null) {
      try {
        final result = await FirebaseFunctions.instance
            .httpsCallable('deleteFirestoreDocAndSyncLark')
            .call(
          {
            "db": 'truckVerification',
            "id": truck.id,
            "recordId": truck.recordId
          },
        );
        return (result.data as Map?)?['code'] == 0;
      } catch (e) {
        debugPrint('$e');
      }
    }

    return false;
  }

  Future<bool> deleteBankInfo(BankInfo bank) async {
    if (User.shared.user?.uid == bank.uid && bank.recordId != null) {
      try {
        final result = await FirebaseFunctions.instance
            .httpsCallable('deleteFirestoreDocAndSyncLark')
            .call(
          {"db": 'bankInfo', "id": bank.id, "recordId": bank.recordId},
        );
        return (result.data as Map?)?['code'] == 0;
      } catch (e) {
        debugPrint('$e');
      }
    }

    return false;
  }
}
