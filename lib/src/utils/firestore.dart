import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:driver/src/model/firestore.dart' as model;
import 'package:driver/src/model/user.dart';
import 'package:flutter/material.dart';

class Firestore {
  static Firestore shared = Firestore();

  Future<List<model.CityQuote>> getLatestQuotesForCities(
      {List<String> cities = const [],
      List<String> types = const [],
      int daysAgo = 7,
      bool distinctCity = true,
      bool allowSameQuoteParams = false,
      void Function(model.CityQuote)? progressCallback}) async {
    List<model.CityQuote> quotes = [];

    performQuery(Query query) async {
      final res = await query.get();
      for (final doc in res.docs) {
        final data = doc.data() as Map;
        if (!allowSameQuoteParams &&
            quotes
                .where((e) =>
                    e.destination == data['destination'] &&
                    e.typeName == data['typeName'])
                .isNotEmpty) {
          continue;
        }
        if (distinctCity &&
            quotes
                .where((e) => e.destination == data['destination'])
                .isNotEmpty) {
          continue;
        }
        model.CityQuote quote = model.CityQuote(
            destination: data['destination'],
            quotation: data['quotation'],
            type: data['type'],
            typeName: data['typeName'],
            created: (data['created'] as Timestamp).toDate(),
            updated: (data['updated'] as Timestamp).toDate(),
            delta: double.parse('${data['delta'] ?? 0}'));
        quotes.add(quote);
        if (progressCallback != null) {
          progressCallback(quote);
        }
      }
    }

    try {
      CollectionReference ref =
          FirebaseFirestore.instance.collection('quotationCollections');
      DateTime start = DateTime.now().subtract(Duration(days: daysAgo));
      Query query = ref
          .where('created', isGreaterThan: Timestamp.fromDate(start))
          .orderBy('created', descending: true);
      if (cities.isNotEmpty) {
        query = query.where('destination', whereIn: cities);
      }
      if (types.isNotEmpty) {
        query = query.where('typeName', whereIn: types);
      }
      await performQuery(query);
    } catch (e) {
      debugPrint('Error: $e');
    }

    return quotes;
  }

  Future<List<model.Order>> getOrders(
      {String status = '找车中',
      int limit = 10,
      String orderBy = 'updatedAt',
      bool descending = false}) async {
    List<model.Order> orders = [];
    try {
      CollectionReference ref = FirebaseFirestore.instance.collection('orders');
      final res = await ref
          .where('status', isEqualTo: status)
          .orderBy(orderBy, descending: descending)
          .limit(limit)
          .get();
      for (var doc in res.docs) {
        final data = doc.data() as Map<String, dynamic>;
        orders.add(model.Order.fromMap(data));
      }
    } catch (e) {
      debugPrint('Error: $e');
    }
    return orders;
  }

  Future<Map<String, dynamic>?> getDriverVerification() async {
    if (User.shared.hasRegistered()) {
      String uid = User.shared.user!.uid;
      try {
        DocumentReference ref = FirebaseFirestore.instance
            .collection('driverVerification')
            .doc(uid);
        DocumentSnapshot doc = await ref.get();
        if (doc.exists) {
          return Map<String, dynamic>.from(doc.data() as Map);
        }
      } catch (e) {
        debugPrint('Error: $e');
      }
    }
    return null;
  }

  Future<List<Map<String, dynamic>>> getTruckVerifications() async {
    if (User.shared.hasRegistered()) {
      String uid = User.shared.user!.uid;
      try {
        CollectionReference ref =
            FirebaseFirestore.instance.collection('truckVerification');
        Query query =
            ref.where('uid', isEqualTo: uid).where('deleted', isNull: true);
        QuerySnapshot docs = await query.get();
        if (docs.size > 0) {
          return docs.docs.map((e) {
            Map<String, dynamic> data =
                Map<String, dynamic>.from(e.data() as Map);
            data['id'] = e.id;
            return data;
          }).toList();
        }
      } catch (e) {
        debugPrint('Error: $e');
      }
    }
    return [];
  }

  Future<Map<String, dynamic>?> createDriverVerification(
      {required String phone,
      required String name,
      required String passport,
      String? chinesePhone,
      String? wechat,
      model.Driver? driver}) async {
    if (User.shared.hasRegistered()) {
      String uid = User.shared.user!.uid;
      try {
        DocumentReference ref = FirebaseFirestore.instance
            .collection('driverVerification')
            .doc(uid);
        DocumentSnapshot doc = await ref.get();
        if (doc.exists) {
          return Map<String, dynamic>.from(doc.data() as Map);
        }
        Map<String, dynamic> data = {
          'phone': phone,
          'name': name,
          'passport': passport,
          'status': model.VerificationStatus.pending.name,
          'chinesePhone': chinesePhone,
          'wechat': wechat,
          'driverId': driver?.id,
          'uid': uid,
          'created': Timestamp.now()
        };

        await ref.set(data);
        return data;
      } catch (e) {
        debugPrint('Error: $e');
      }
    }

    return null;
  }

  Future<Map<String, dynamic>?> createTruckVerification(
      {required String license,
      required String licenseStorage,
      required String type,
      required String tonnage,
      required String volume,
      model.Truck? truck}) async {
    if (User.shared.hasRegistered()) {
      String uid = User.shared.user!.uid;
      try {
        CollectionReference ref =
            FirebaseFirestore.instance.collection('truckVerification');

        Map<String, dynamic> data = {
          'uid': uid,
          'license': license,
          'licenseStorage': licenseStorage,
          'type': type,
          'tonnage': tonnage,
          'volume': volume,
          'truckId': truck?.id,
          'status': model.VerificationStatus.pending.name,
          'created': Timestamp.now(),
          'deleted': null
        };

        final added = await ref.add(data);
        data['id'] = added.id;
        return data;
      } catch (e) {
        debugPrint('Error: $e');
      }
    }

    return null;
  }

  Future<Map<String, dynamic>?> createBankInfo(model.BankInfo bankInfo) async {
    if (User.shared.hasRegistered()) {
      String uid = User.shared.user!.uid;
      try {
        CollectionReference ref =
            FirebaseFirestore.instance.collection('bankInfo');

        Map<String, dynamic> data = {
          'uid': uid,
          'type': bankInfo.type.name,
          'created': Timestamp.now(),
          'deleted': null
        };

        if (bankInfo.type == model.BankType.individual) {
          model.IndividualBankInfo info = bankInfo as model.IndividualBankInfo;
          data['name'] = info.name;
          data['account'] = info.account;
          data['bank'] = info.bank;
        } else {
          model.BusinessBankInfo info = bankInfo as model.BusinessBankInfo;
          data['name'] = info.name;
          data['bank'] = info.bank;
          data['account'] = info.account;
          data['swift'] = info.swift;
          data['address'] = info.address;
          data['taxNumber'] = info.taxNumber;
          data['beneficiaryCode'] = info.beneficiaryCode;
          data['usageCode'] = info.usageCode;
        }
        final added = await ref.add(data);
        data['id'] = added.id;
        return data;
      } catch (e) {
        debugPrint('Error: $e');
      }
    }

    return null;
  }

  Future<List<Map<String, dynamic>>> getBankInfos() async {
    if (User.shared.hasRegistered()) {
      String uid = User.shared.user!.uid;
      try {
        CollectionReference ref =
            FirebaseFirestore.instance.collection('bankInfo');
        Query query =
            ref.where('uid', isEqualTo: uid).where('deleted', isNull: true);
        QuerySnapshot docs = await query.get();
        if (docs.size > 0) {
          return docs.docs.map((e) {
            Map<String, dynamic> data =
                Map<String, dynamic>.from(e.data() as Map);
            data['id'] = e.id;
            return data;
          }).toList();
        }
      } catch (e) {
        debugPrint('Error: $e');
      }
    }
    return [];
  }
}
