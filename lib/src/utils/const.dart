import 'dart:ui';

import 'package:driver/src/settings/controller.dart';

const Color primaryColor = Color(0xFF029FB0);
const Color backgroundColor = Color(0xFFFAFAFF);
const Color errorColor = Color(0xFFC63B58);
const Color warningColor = Color(0xFFFF752D);

String truckImage(String type,
    {bool isSelected = false, bool ignoreSelection = false}) {
  String img = SettingsController.shared.truckTypes
          .where((e) => e.name == type)
          .firstOrNull
          ?.img ??
      'xslcc';
  return 'assets/images/truck/${ignoreSelection ? '' : (isSelected ? 'select-' : 'unselect-')}$img.png';
}

const Map<String, String> orderStatusSubtitles = {
  '待接单': '目前无人接单，快来抢单吧~',
  '找车中': '目前无人接单，快来抢单吧~'
};
