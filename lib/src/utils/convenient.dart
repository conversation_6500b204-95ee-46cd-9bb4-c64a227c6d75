import 'package:driver/src/app.dart';
import 'package:driver/src/localization/app_localizations.dart'
    show AppLocalizations;
import 'package:driver/src/model/user.dart';
import 'package:driver/src/settings/controller.dart';
import 'package:flutter/material.dart';

AppLocalizations appLoc(BuildContext context) {
  if (context.mounted) {
    return AppLocalizations.of(context)!;
  }

  return AppLocalizations.of(MyApp.materialKey.currentContext!)!;
}

String localizedString(String str) {
  return SettingsController.shared.translate(str);
}

String maskPrice(User user, int price) {
  String str = '$price';
  if (user.driverVerification != null) return str;
  if (str.isEmpty) {
    return str;
  }
  if (str.length < 3) {
    return '${str[0]}*';
  }
  return str.substring(0, str.length ~/ 3) +
      '*' * (str.length - 2 * (str.length ~/ 3)) +
      str.substring(str.length - str.length ~/ 3);
}

extension DateTimeFormat on DateTime {
  String toDateString() {
    return toIso8601String().split('T')[0];
  }

  String toMinuteString() {
    List<String> split = toIso8601String().split('T');
    return '${split[0]} ${split[1].substring(0, 5)}';
  }
}
