import 'package:driver/src/mixins/entry.dart';
import 'package:driver/src/model/firestore.dart';
import 'package:driver/src/truck/info_entry.dart';
import 'package:driver/src/utils/convenient.dart';
import 'package:driver/src/mixins/page.dart';
import 'package:driver/src/utils/functions.dart';
import 'package:flutter/material.dart';
import 'package:lsapp/lsapp.dart';

class TruckLicenseEntryView extends StatefulWidget {
  const TruckLicenseEntryView({super.key});

  static const String routeName = 'truck_license_entry';

  @override
  State<StatefulWidget> createState() => _TruckLicenseEntryViewState();
}

class TruckLicenseEntry {
  TruckLicenseEntry(
      {required this.license, required this.licenseStorage, this.truck});

  final String license;
  final String licenseStorage;
  final Truck? truck;
}

class _TruckLicenseEntryViewState extends State<TruckLicenseEntryView>
    with UnfocusMixin, EntryMixin, PageMixin {
  final TextEditingController _license = TextEditingController();
  String? _licenseStorage;
  Truck? _selectedTruck;

  @override
  Widget build(BuildContext context) {
    return unfocusContainer(pageBase(appLoc(context).truckManagement,
        Column(children: [license(), licenseImage()]),
        operation: nextButton()));
  }

  Widget license() {
    return formItem(
        title: appLoc(context).license,
        isRequired: true,
        input: textInput(_license, appLoc(context).licensePlaceholder,
            onBlur: findTruckInfoByLicense, useUpperCase: true));
  }

  Widget licenseImage() {
    double width = MediaQuery.of(context).size.width - 44;
    double height = width * 390 / 656;
    return formItem(
        title: appLoc(context).licenseImage,
        isRequired: true,
        input: imageUpload(_licenseStorage, (value) {
          setState(() {
            _licenseStorage = value;
          });
        },
            width: width,
            height: height,
            placeholder: Image.asset('assets/images/license_add.png',
                width: width, height: height)));
  }

  BottomOperationPanel nextButton() {
    return BottomOperationPanel.singleOperation(
        context,
        appLoc(context).next,
        _licenseStorage != null && _license.text.isNotEmpty
            ? () => Navigator.of(context).pushNamed(
                TruckInfoEntryView.routeName,
                arguments: TruckLicenseEntry(
                    license: _license.text,
                    licenseStorage: _licenseStorage!,
                    truck: _selectedTruck))
            : null);
  }

  Future findTruckInfoByLicense() async {
    await findInfo(Functions.shared.findTrucksWithLicense, _license.text,
        (selected) async {
      _selectedTruck = selected;
      setState(() {
        _license.text = selected.value;
        _licenseStorage = selected.license1ImgStorage;
      });
      String? path = await getStorageImage('getLicenseFromLark', selected);
      if (path != null) {
        setState(() {
          _selectedTruck?.license1ImgStorage = path;
          _licenseStorage = path;
        });
      }
    });
  }
}
