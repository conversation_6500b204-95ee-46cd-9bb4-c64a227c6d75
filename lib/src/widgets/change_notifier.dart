import 'package:driver/src/model/user.dart';
import 'package:driver/src/settings/controller.dart';
import 'package:flutter/cupertino.dart';
import 'package:provider/provider.dart';

ChangeNotifierProvider<User> userProvider(Widget Function(User) child) {
  return ChangeNotifierProvider.value(
      value: User.shared,
      child: Consumer<User>(builder: (_, user, __) {
        return child(user);
      }));
}

ChangeNotifierProvider<SettingsController> settingProvider(
    Widget Function(SettingsController) child) {
  return ChangeNotifierProvider.value(
      value: SettingsController.shared,
      child: Consumer<SettingsController>(builder: (_, controller, __) {
        return child(controller);
      }));
}
