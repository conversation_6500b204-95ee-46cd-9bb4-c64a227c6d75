import 'package:driver/src/model/user.dart';
import 'package:flutter/material.dart';
import 'package:lsapp/lsapp.dart';
import 'package:webview_flutter/webview_flutter.dart';

enum WebResource { policy, terms, faq, disclaimers }

class WebView extends StatelessWidget {
  final WebResource? resource;
  final String? initUrl;

  const WebView({super.key, this.resource, this.initUrl});
  static const appHost = '';
  static String policyUrl = 'https://$appHost/privacy.html';
  static String termsUrl = 'https://$appHost/terms.html';
  static String disclaimersUrl = 'https://$appHost/disclaimers.html';
  static String faqUrl() =>
      'https://$appHost/faq_${Device.shared.platform.toLowerCase()}.html';

  static void open(BuildContext context, WebResource res) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => WebView(resource: res)),
    );
  }

  static void openUrl(BuildContext context, String url) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => WebView(initUrl: url)),
    );
  }

  @override
  Widget build(BuildContext context) {
    String url = '';
    if (resource == WebResource.policy) {
      url = policyUrl;
    } else if (resource == WebResource.terms) {
      url = termsUrl;
    } else if (resource == WebResource.disclaimers) {
      url = disclaimersUrl;
    } else if (resource == WebResource.faq) {
      url = faqUrl();
    } else if (initUrl != null) {
      url = initUrl!;
    } else {
      return const SizedBox();
    }

    WebViewController controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(const Color(0x00000000))
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {},
          onPageStarted: (String url) {
            debugPrint('onPageStarted $url');
            if (url.startsWith(
                'https://$appHost/?link=https://$appHost/verify?email')) {
              Future.delayed(const Duration(milliseconds: 200), () {
                User.shared.reload();
                if (context.mounted) {
                  Navigator.of(context).pop();
                }
              });
            }
          },
          onPageFinished: (String url) {},
          onWebResourceError: (WebResourceError error) {},
          onNavigationRequest: (NavigationRequest request) {
            return NavigationDecision.navigate;
          },
        ),
      )
      ..loadRequest(Uri.parse(url));

    return Scaffold(
        appBar: AppBar(
          backgroundColor: Colors.white,
        ),
        body: WebViewWidget(controller: controller));
  }
}
