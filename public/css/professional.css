/* Professional Black & White Document Style */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
  line-height: 1.6;
  color: #333 !important;
  background-color: #ffffff !important;
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

/* Reset all color styles to black and white */
* {
  color: #333 !important;
  background-color: transparent !important;
}

body, html {
  background-color: #ffffff !important;
}

.container, .WordSection1, div[class*="WordSection"] {
  background: #ffffff !important;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  padding: 40px 30px;
  margin: 0 auto;
}

/* Headers */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600 !important;
  color: #000 !important;
  margin-bottom: 20px !important;
  padding-bottom: 8px !important;
  border-bottom: 1px solid #ddd !important;
  line-height: 1.3 !important;
}

h1 {
  font-size: 28px !important;
  text-align: center !important;
  border-bottom: 2px solid #333 !important;
  padding-bottom: 15px !important;
  margin-bottom: 30px !important;
}

h2 {
  font-size: 22px !important;
}

h3 {
  font-size: 18px !important;
}

/* Paragraphs */
p, .MsoNormal {
  margin-bottom: 15px !important;
  font-size: 14px !important;
  line-height: 1.7 !important;
  color: #333 !important;
  text-align: left !important;
  text-justify: none !important;
}

/* Lists */
ul, ol {
  margin: 15px 0 !important;
  padding-left: 25px !important;
}

li, .MsoNormal li {
  margin-bottom: 8px !important;
  font-size: 14px !important;
  line-height: 1.6 !important;
  color: #333 !important;
}

/* Tables */
table, .MsoNormalTable {
  width: 100% !important;
  border-collapse: collapse !important;
  margin: 20px 0 !important;
  background: #ffffff !important;
  border: 1px solid #ddd !important;
}

th, td {
  padding: 12px !important;
  border: 1px solid #ddd !important;
  font-size: 13px !important;
  vertical-align: top !important;
  color: #333 !important;
  background-color: transparent !important;
}

th {
  background: #f8f9fa !important;
  color: #333 !important;
  font-weight: 600 !important;
  text-align: center !important;
  font-size: 14px !important;
}

tr:nth-child(even) td {
  background-color: #f9f9f9 !important;
}

/* Strong/Bold text */
strong, b {
  font-weight: 600 !important;
  color: #000 !important;
}

/* Dividers */
hr {
  border: none !important;
  height: 1px !important;
  background: #ddd !important;
  margin: 30px 0 !important;
}

/* Code and Pre */
code {
  background: #f4f4f4 !important;
  border: 1px solid #ddd !important;
  border-radius: 3px !important;
  padding: 2px 4px !important;
  font-family: "Courier New", monospace !important;
  font-size: 13px !important;
  color: #333 !important;
}

pre {
  background: #f8f9fa !important;
  border: 1px solid #ddd !important;
  border-radius: 4px !important;
  padding: 15px !important;
  margin: 15px 0 !important;
  overflow-x: auto !important;
  font-family: "Courier New", monospace !important;
  font-size: 13px !important;
  line-height: 1.4 !important;
  color: #333 !important;
}

/* Special boxes */
.highlight-box, .warning-box, .contact-info {
  background: #f8f9fa !important;
  border: 1px solid #ddd !important;
  border-left: 4px solid #333 !important;
  padding: 20px !important;
  margin: 20px 0 !important;
  border-radius: 0 4px 4px 0 !important;
}

/* Remove all inline styles that might override */
[style*="color"] {
  color: #333 !important;
}

[style*="background"] {
  background-color: transparent !important;
}

[style*="font-family"] {
  font-family: inherit !important;
}

/* Spans - clean up */
span {
  color: inherit !important;
  background-color: transparent !important;
}

/* Center alignment for specific elements */
div[align="center"], .center {
  text-align: center !important;
}

/* Remove Word-specific styling */
.MsoChpDefault,
.MsoNormal,
[class*="Mso"] {
  font-size: inherit !important;
  font-family: inherit !important;
  margin: inherit !important;
  color: inherit !important;
}

/* Responsive design */
@media (max-width: 768px) {
  body {
    padding: 10px !important;
  }
  
  .container, .WordSection1, div[class*="WordSection"] {
    padding: 20px 15px !important;
  }
  
  h1 {
    font-size: 24px !important;
  }
  
  h2 {
    font-size: 20px !important;
  }
  
  h3 {
    font-size: 16px !important;
  }
  
  table, .MsoNormalTable {
    font-size: 12px !important;
  }
  
  th, td {
    padding: 8px 6px !important;
  }
}

/* Print styles */
@media print {
  body {
    background: white !important;
    color: black !important;
  }
  
  .container, .WordSection1 {
    box-shadow: none !important;
    border: none !important;
  }
}

/* Clean up any remaining color artifacts */
body * {
  color: inherit !important;
}

/* Ensure proper text hierarchy */
h1, h2, h3 {
  color: #000 !important;
}

p, li, td, span {
  color: #333 !important;
}

/* Final override for any stubborn elements */
.WordSection1 * {
  color: #333 !important;
}

.WordSection1 h1,
.WordSection1 h2, 
.WordSection1 h3,
.WordSection1 strong,
.WordSection1 b {
  color: #000 !important;
}
